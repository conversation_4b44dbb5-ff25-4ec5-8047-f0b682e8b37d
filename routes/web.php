<?php

use App\Http\Controllers\Auth\EmailActionsController;
use App\Http\Controllers\Auth\JoinController;
use App\Http\Controllers\Auth\SocialLoginController;
use App\Http\Controllers\Auth\SocialMissingDataController;
use App\Http\Controllers\Auth\TwoFactorController;
use App\Http\Controllers\Prediction\ClubController;
use App\Http\Controllers\Prediction\CountryController;
use App\Http\Controllers\Prediction\FiltersController;
use App\Http\Controllers\Prediction\FixtureController;
use App\Http\Controllers\Prediction\LeagueController;
use App\Http\Controllers\Prediction\PlayerController;
use App\Http\Controllers\Profile\DevicesController;
use App\Http\Controllers\Profile\PhoneActivateController;
use App\Http\Controllers\Profile\PhoneChangeController;
use App\Http\Controllers\Profile\ProfileInformationController;
use App\Http\Controllers\Profile\ProfileSubscriptionController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\LeagueController as LeagueManagmentController;
use App\Http\Controllers\Admin\PlanController;
use App\Http\Controllers\Admin\SubscriptionController;
use App\Http\Controllers\User\SavedFiltersController;
use App\Http\Middleware\FixtureExistVerificationMiddleware;
use App\Http\Middleware\UserVerificationMiddleware;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\ImpersonationController;

Route::get('/join', [JoinController::class, 'index'])->name('join');
Route::post('/join', [JoinController::class, 'store']);

Route::get('/social/{provider}', [SocialLoginController::class, 'redirectToProvider'])->where('provider', 'facebook|google')->name('social.login');
Route::get('/social/facebook/callback', [SocialLoginController::class, 'handleFacebookCallback']);
Route::get('/social/google/callback', [SocialLoginController::class, 'handleGoogleCallback']);
Route::post('/social/google-one-click/callback', [SocialLoginController::class, 'handleGoogleOneClickCallback']);

Route::get('/', function () {
    return view('welcome');
})->name('home');

// Static pages
Route::get('/privacy-policy', function () {
    return view('static.privacy-policy');
})->name('privacy.policy');

Route::get('/terms-of-service', function () {
    return view('static.terms-of-service');
})->name('terms.service');

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
    UserVerificationMiddleware::class,
])->group(function () {

    Route::group(['as' => '2fa.', 'prefix' => '2fa'], function () {
        Route::get('/challenge', [TwoFactorController::class, 'form'])->name('challenge');
        Route::post('/challenge', [TwoFactorController::class, 'store'])->name('challenge.store');
        Route::post('/challenge/resend', [TwoFactorController::class, 'resend'])->name('challenge.resend');
    });

    Route::group(['as' => 'auth.', 'prefix' => 'auth'], function () {
        Route::get('/social-missing-data', [SocialMissingDataController::class, 'form'])->name('social-missing-data.form');
        Route::post('/social-missing-data', [SocialMissingDataController::class, 'store'])->name('social-missing-data.store');
    });

    Route::group(['as' => 'profile.', 'prefix' => 'profile'], function () {
        Route::get('/phone-change', [PhoneChangeController::class, 'form'])->name('phone-change');
        Route::post('/phone-change', [PhoneChangeController::class, 'store'])->name('phone-change.store');
        Route::post('/phone-change/reset', [PhoneChangeController::class, 'reset'])->name('phone-change.reset');

        Route::get('/phone-activate', [PhoneActivateController::class, 'form'])->name('phone-activate');
        Route::post('/phone-activate', [PhoneActivateController::class, 'store'])->name('phone-activate.store');
        Route::post('/phone-activate/resend', [PhoneActivateController::class, 'resend'])->name('phone-activate.resend');

        Route::get('/user', [ProfileInformationController::class, 'form'])->name('user-information');
        Route::put('/user', [ProfileInformationController::class, 'update']);

        Route::get('/payments-history', [ProfileSubscriptionController::class, 'paymentsHistory'])->name('payments-history');

        Route::get('/subscriptions', [ProfileSubscriptionController::class, 'index'])->name('subscriptions');
        Route::get('/subscriptions/select', [ProfileSubscriptionController::class, 'select'])->name('select-subscription');
        Route::get('/subscriptions/payment', [ProfileSubscriptionController::class, 'paymentMethodForm'])->name('payments-method-form');
        Route::get('/subscriptions/buy/{plan}', [ProfileSubscriptionController::class, 'buy'])->name('buy-subscription');
        Route::get('/subscriptions/activate/{subscriptionId}', [ProfileSubscriptionController::class, 'activate'])->name('activate-subscription');
        Route::get('/subscriptions/cancel/{subscriptionId}', [ProfileSubscriptionController::class, 'cancel'])->name('cancel-subscription');
        Route::get('/subscriptions/price-change', [ProfileSubscriptionController::class, 'priceChangeForm'])->name('price-change-form');

        Route::resource('devices', DevicesController::class)->only(['index',  'destroy']);

        Route::get('/saved-filters', [SavedFiltersController::class, 'index'])->name('saved-filters-list');
        Route::get('/saved-filters/apply/{filterId}', [SavedFiltersController::class, 'apply'])->name('saved-filters-apply');
        Route::post('/saved-filters/delete/{filterId}', [SavedFiltersController::class, 'delete'])->name('saved-filters-delete');
    });

    Route::post('/saved-filters', [SavedFiltersController::class, 'save'])->name('saved-filters');

    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::get('filters', [FiltersController::class, 'index'])->name('filters.index');
    Route::post('filters', [FiltersController::class, 'filterPost'])->name('filters.filter');
});


Route::group(['as' => 'action.', 'prefix' => 'action'], function () {
    Route::get('/email/{token}', [EmailActionsController::class, 'action'])->name('email-confirm');
});


Route::get('leagues', [LeagueController::class, 'index'])->name('league.index');

Route::get('/club', [ClubController::class, 'index'])->name('club.index');
Route::get('/club/{club:slug}', [ClubController::class, 'show'])->name('club.show');
Route::get('/player/{player:slug}', [PlayerController::class, 'show'])->name('player.show');

Route::group([
    'prefix' => 'fixture/{fixtureSlug}',
    'as' => 'fixture.',
    'middleware' => [FixtureExistVerificationMiddleware::class]
], function () {
    Route::get('', [FixtureController::class, 'show'])->name('show');
    Route::get('/player/{player:slug}', [FixtureController::class, 'showPlayer'])->name('player.show');
});

Route::get('/fixtures', [FixtureController::class, 'index'])->name('fixture.index');

Route::group(['middleware' => ['role:admin']], function () {
    Route::get('/admin/users', [UserController::class, 'index'])->name('admin.users-index');
    Route::get('/admin/users/{user}/edit', [UserController::class, 'edit'])->name('admin.users-edit');
    Route::get('/admin/leagues', [LeagueManagmentController::class, 'index'])->name('admin.leagues-index');
    Route::get('/admin/plans', [PlanController::class, 'list'])->name('admin.plans-list');
    Route::get('/admin/plans/create', [PlanController::class, 'create'])->name('admin.plans-create');
    Route::get('/admin/plans/{plan}/edit', [PlanController::class, 'edit'])->name('admin.plans-edit');
    Route::get('/admin/plans/{plan}/info', [PlanController::class, 'info'])->name('admin.plans-info');
    Route::get('/admin/subscriptions', [SubscriptionController::class, 'list'])->name('admin.subscriptions-list');
    Route::get('/admin/impersonation/{id}', [ImpersonationController::class, 'impersonate'])->name('admin.impersonate');

});

Route::group(['prefix' => '{country:slug}'], function() {
    Route::group(['prefix' => '{league:slug}', 'as' => 'league.'], function() {
        Route::get('/standing', [LeagueController::class, 'showStandingTable'])->name('standing');
        Route::get('/fixtures', [LeagueController::class, 'showFixturesList'])->name('fixtures');
        Route::get('/', [LeagueController::class, 'show'])->name('show');

        Route::group(['prefix' => '{year}', 'as' => 'year.'], function() {
            Route::get('/standing', [LeagueController::class, 'showStandingTable'])->name('standing');
            Route::get('/fixtures', [LeagueController::class, 'showFixturesList'])->name('fixtures');
            Route::get('/', [LeagueController::class, 'show'])->name('show');

        });

    });

    Route::get('/', [CountryController::class, 'show'])->name('country.show');
});

