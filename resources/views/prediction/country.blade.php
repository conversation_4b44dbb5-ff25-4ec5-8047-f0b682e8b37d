@php
$sumProgress = function($fixtures_count, $upcoming_fixtures_count) {
    if ($fixtures_count == 0) {
        return 0;
    }

    $passed_fixtures_count = $fixtures_count - $upcoming_fixtures_count;

    return (int) ($passed_fixtures_count / $fixtures_count * 100);
};
@endphp

<x-app-layout
    :breadcrumbs="[
        [
            'label' => config('app.name', 'Laravel'),
            'url' => route('home')
        ],
        [
            'label' => __('league.all'),
            'url' => route('league.index')
        ], 
        [
            'label' => $country->name,
            'icon' => $country->cachedFlag
        ]
    ]"
>
    <div class="py-6 flex justify-center">
        <div class="grid grid-cols-1 md:grid-cols-8 gap-4 w-[1200px]">

            <x-tile class="col-span-5">

                @foreach($country->leagues as $league)
                    <a href="{{ route('league.show', ['country' => $country->slug, 'league' => $league->slug ?? 'error']) }}" class="block p-4 hover:bg-gray-100">
                        <div class="grid grid-cols-1 md:grid-cols-2">
                            <div class="flex items-center gap-2">
                                <img src="{{ $league->cachedLogo }}" class="w-8 h-8" />
                                <div>
                                    <p class="font-bold">
                                        {{ $league->name }}
                                    </p>
                                    <p class="text-gray-500 text-xs">
                                        {{ __('league.season')}}
                                        @isset($league->lastSeason)
                                            {{$league->lastSeason->year}}
                                        @else 
                                            {{ __('league.no-season') }}
                                        @endisset
                                    </p>
                                </div>
                            </div>

                            <div class="grid grid-cols-3">
                                <div class="flex flex-col items-center gap-1">
                                    <p class="text-sm text-gray-500">{{ __('fixture.count') }}</p>
                                    <p class="text-sm">{{ $league->lastSeason->fixtures_count ?? 0 }}</p>
                                </div>

                                <div class="flex flex-col items-center gap-1">
                                    <p class="text-sm text-gray-500">{{ __('fixture.goal-count') }}</p>
                                    <p class="text-sm">{{ $league->lastSeason->total_goals ?? 0 }}</p>
                                </div>

                                <div class="flex flex-col items-center gap-1">
                                    <p class="text-sm text-gray-500">{{ __('league.progress') }}</p>
                                    <div class="w-24">
                                        <x-progress-bar :progress="$sumProgress($league->lastSeason->fixtures_count ?? 0, $league->lastSeason->upcoming_fixtures_count ?? 0)" />
                                    </div>
                                </div>
                            </div>
                        </div>

                    </a>

                    @if(!$loop->last)
                        <hr class="mt-1 mb-1" />
                    @endif
                @endforeach

            </x-tile>

            <div class="col-span-3 flex flex-col gap-2">
                <x-tile :title="__('player.top-goalscorers')">
                    <x-resource-table 
                        class="min-w-[350px]" 
                        :columns="[
                            ['key' => 'number', 'label' => __('player.number-short'), 'tooltip' => __('player.number'), 'className' => 'w-3'],
                            ['key' => 'name', 'label' => __('player.singular'), 'className' => ''],
                            ['key' => 'value', 'label' => '', 'className' => 'w-4'],
                            ['key' => 'show', 'label' => '', 'className' => ''],
                        ]" 
                        :resources="[
                            'data' => [],
                        ]"
                    >
                        
                    </x-resource-table>
                </x-tile>
                <x-tile :title="__('player.top-assists')">
                    <x-resource-table 
                        class="min-w-[350px]" 
                        :columns="[
                            ['key' => 'number', 'label' => __('player.number-short'), 'tooltip' => __('player.number'), 'className' => 'w-3'],
                            ['key' => 'name', 'label' => __('player.singular'), 'className' => ''],
                            ['key' => 'value', 'label' => '', 'className' => 'w-4'],
                            ['key' => 'show', 'label' => '', 'className' => ''],
                        ]" 
                        :resources="[
                            'data' => [],
                        ]"
                    >
                        
                    </x-resource-table>
                </x-tile>
                <x-tile :title="__('player.top-total-goals')">
                    <x-resource-table 
                        class="min-w-[350px]" 
                        :columns="[
                            ['key' => 'number', 'label' => __('player.number-short'), 'tooltip' => __('player.number'), 'className' => 'w-3'],
                            ['key' => 'name', 'label' => __('player.singular'), 'className' => ''],
                            ['key' => 'value', 'label' => '', 'className' => 'w-4'],
                            ['key' => 'show', 'label' => '', 'className' => ''],
                        ]" 
                        :resources="[
                            'data' => [],
                        ]"
                    >
                        
                    </x-resource-table>
                </x-tile>
            </div>

        </div>
    </div>
</x-app-layout>
