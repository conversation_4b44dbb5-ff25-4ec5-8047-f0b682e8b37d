<div>
    <div x-data="{ showDeleteConfirm: @entangle('showDeleteConfirm') }">
        <div class="accordion mb-4" id="filtersAccordion">
            <div class="card">
                <div wire:ignore.self aria-labelledby="simpleFilters">
                    <div class="card-body" class="flex flex-col flex-wrap w-11/12 ml-4">
                        <div class="flex">
                            <x-app-input wire:model.live.debounce.500ms="filter" divClass="flex-1 mx-2"
                                :name="__('filter.search')" />
                        </div>
                    </div>
                </div>
            </div>

        <table class="table-auto w-full">
            <thead class="bg-gray-300">
                <tr>
                    <x-table-header-sort wire:click="sort('id')" :isSorting="$sortColumn == 'id'"
                        :sortDescending="$sortDescending">ID</x-table-header-sort>
                    <x-table-header-sort wire:click="sort('email')" :isSorting="$sortColumn == 'email'"
                        :sortDescending="$sortDescending">@lang('user.email')</x-table-header-sort>
                    <x-table-header-sort wire:click="sort('nickname')" :isSorting="$sortColumn == 'name'"
                        :sortDescending="$sortDescending">@lang('user.nickname')</x-table-header-sort>
                    <x-table-header-sort wire:click="sort('phone')" :isSorting="$sortColumn == 'phone'"
                        :sortDescending="$sortDescending">@lang('user.phone')</x-table-header-sort>
                    <th class="p-2 border-gray-500 border">@lang('user.status')</th>
                    <th class="p-2 border-gray-500 border">@lang('user.lastLogin')</th>
                    <th class="p-2 border-gray-500 border">@lang('user.ip')</th>
                    <th class="p-2 border-gray-500 border">@lang('user.plan')</th>
                    <th class="p-2 border-gray-500 border">@lang('common.actions')</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($users as $user)
                    <tr class={{ $user->is_suspicious ? "bg-red-200" : "even:bg-gray-200" }}>
                        <td class="p-2 border-gray-500 border">{{ $user->id }}</td>
                        <td class="p-2 border-gray-500 border">{{ $user->email }}</td>
                        <td class="p-2 border-gray-500 border">{{ $user->nickname }}</td>
                        <td class="p-2 border-gray-500 border">{{ $user->phone }}</td>
                        <td class="p-2 border-gray-500 border">{{ is_null($user->deleted_at) ? 'Aktywny' : 'Nieaktywny' }}</td>
                        <td class="p-2 border-gray-500 border">{{ $user->lastActivity() }}</td>
                        <td class="p-2 border-gray-500 border">{{ optional($user->sessions->first())->ip_address ?? 'N/A' }}</td>
                        <td class="p-2 border-gray-500 border">{{ $user->plan_id ? __('user.plans.' . $user->plan_id) : '' }}</td>
                        <td class="p-2 border-gray-500 border">
                            <x-wireui-dropdown icon="bars-3">
                                <x-wireui-dropdown.item href="{{ route('admin.users-edit', ['user' => $user->id]) }}" title="@lang('user.edit')">
                                    @lang('user.edit')
                                </x-wireui-dropdown.item>
                                <x-wireui-dropdown.item
                                    onclick="Livewire.dispatch('openModal', { component: 'admin.profile.user-delete', arguments: { user: {{ $user->id }} }})">
                                    @lang('user.delete')
                                </x-wireui-dropdown.item>
                                <x-wireui-dropdown.item
                                    wire:click="userLogin({{$user->id}}}})">
                                    @lang('user.login') fdf
                                </x-wireui-dropdown.item>
                            </x-wireui-dropdown>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        {{ $users->links() }}
    </div>
    </div>

@push('styles')
    <style>
        td,
        th {
            padding: 4px;
        }

        .btn.focus,
        .btn:focus {
            box-shadow: none;
        }

        .accordion>.card {
            overflow: unset;
        }
    </style>
@endpush
