<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('profile.your-data') }}
        </h2>
    </x-slot>

    <div class="grid grid-cols-2 md:grid-cols-7 max-w-7xl mx-auto py-10 sm:px-6 lg:px-8 items-start gap-5">
        <x-sub-menu.profile />
        <div class="col-span-5">
            <div class="flex flex-col">
              <div class="overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 sm:px-6 lg:px-8">
                  <div class="overflow-hidden">
                    <table
                      class="min-w-full text-left text-sm font-light text-surface dark:text-white">
                      <thead
                        class="border-b border-neutral-200 bg-white font-medium dark:border-white/10 dark:bg-body-dark">
                        <tr>
                        <th scope="col" class="px-6 py-4">Data</th>
                          <th scope="col" class="px-6 py-4">Numer</th>
                          <th scope="col" class="px-6 py-4">Cena</th>
                          <th scope="col" class="px-6 py-4"></th>
                        </tr>
                      </thead>
                      <tbody>
                        @forelse($invoices as $invoice)
                          <tr
                            class="border-b border-neutral-200 bg-black/[0.02] dark:border-white/10">
                            <td class="whitespace-nowrap px-6 py-4">{{ date('Y-m-d H:i:s', $invoice->created) }}</td>
                            <td class="whitespace-nowrap px-6 py-4 font-medium">{{ $invoice->number }}</td>
                            <td class="whitespace-nowrap px-6 py-4">{{ \Illuminate\Support\Number::currency($invoice->total/100, $invoice->currency) }}</td>
                            <td class="whitespace-nowrap px-6 py-4">
                              <x-a-button :href="$invoice->hosted_invoice_url" target='_blank' rel="noopener noreferrer">@lang('subscription.invoice')</x-a-button>
                            </td>
                          </tr>
                        @empty
                            <td class="whitespace-nowrap px-6 py-4" colspan="3">@lang('common.no_records')</td>
                        @endforelse
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
        </div>
    </div>
</x-app-layout>
