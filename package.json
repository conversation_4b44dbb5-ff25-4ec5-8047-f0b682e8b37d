{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@rollup/wasm-node": "^4.9.6", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "axios": "^1.6.4", "choices.js": "^10.1.0", "laravel-vite-plugin": "^1.0", "livewire-sortable": "^1.0.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^5.0"}, "resolutions": {"rollup": "npm:@rollup/wasm-node"}, "overrides": {"rollup": "npm:@rollup/wasm-node"}, "dependencies": {"@alpinejs/intersect": "^3.14.1", "@alpinejs/mask": "^3.14.0", "@fortawesome/fontawesome-free": "^6.2.0", "@ryangjchandler/alpine-tooltip": "^2.0.0", "alpinejs": "^3.14.0", "alpinejs-masonry": "^1.0.15", "tailwind-scrollbar": "^3.1.0"}}